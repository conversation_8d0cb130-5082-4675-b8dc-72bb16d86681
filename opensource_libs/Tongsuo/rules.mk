# Copyright (C) 2024 The Tongsuo Project.
#
# Permission to use, copy, modify, and/or distribute this software for any
# purpose with or without fee is hereby granted, provided that the above
# copyright notice and this permission notice appear in all copies.
#
# THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
# WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
# MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
# SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
# WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
# OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
# CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

# This file integrates Tongsuo using official build system with symbol prefix for coexistence
# Reference: https://www.tongsuo.net/docs/compilation/source-compilation
# Reference: https://www.tongsuo.net/docs/compilation/openssl-compatible

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

# Tongsuo configuration for official build system integration
TONGSUO_PREFIX := TONGSUO_
TONGSUO_BUILD_DIR := $(BUILDDIR)/tongsuo_build
TONGSUO_INSTALL_DIR := $(abspath $(BUILDDIR)/tongsuo_install)
TONGSUO_SOURCE_DIR := $(LOCAL_DIR)

# Check if we should use official build system or stub implementation
USE_OFFICIAL_BUILD := $(shell test -f $(TONGSUO_SOURCE_DIR)/Configure && echo yes || echo no)

ifeq ($(USE_OFFICIAL_BUILD),yes)
# Official Tongsuo build system integration

# Configuration files generated by official build system
TONGSUO_CONFIG_H := $(TONGSUO_BUILD_DIR)/include/openssl/opensslconf.h
TONGSUO_SYMBOL_PREFIX_H := $(TONGSUO_BUILD_DIR)/include/openssl/symbol_prefix.h
TONGSUO_MAKEFILE := $(TONGSUO_BUILD_DIR)/Makefile

# Determine target platform based on trusty-tee architecture
# Following official documentation: https://www.tongsuo.net/docs/compilation/source-compilation
ifeq ($(TRUSTY_USER_ARCH),arm64)
TONGSUO_TARGET_PLATFORM := linux-aarch64
else ifeq ($(TRUSTY_USER_ARCH),arm)
TONGSUO_TARGET_PLATFORM := linux-armv4
else
# Default fallback
TONGSUO_TARGET_PLATFORM := linux-generic64
endif

# Configure Tongsuo using official Configure script with symbol prefix
# Step 1: Configure with ./Configure (按需配置选项)
$(TONGSUO_CONFIG_H) $(TONGSUO_SYMBOL_PREFIX_H) $(TONGSUO_MAKEFILE): FORCE
	@echo "Configuring Tongsuo with official build system for $(TONGSUO_TARGET_PLATFORM)..."
	@mkdir -p $(TONGSUO_BUILD_DIR)/include/openssl
	@cp $(TONGSUO_SOURCE_DIR)/include/openssl/symhacks.h $(TONGSUO_BUILD_DIR)/include/openssl/
	@cd $(TONGSUO_BUILD_DIR) && \
	$(abspath $(TONGSUO_SOURCE_DIR))/Configure \
		$(TONGSUO_TARGET_PLATFORM) \
		--prefix=$(TONGSUO_INSTALL_DIR) \
		--symbol-prefix=$(TONGSUO_PREFIX) \
		--api=1.1.1 \
		no-shared \
		no-dso \
		no-engine \
		no-tests \
		no-threads \
		enable-sm2 \
		enable-sm3 \
		enable-sm4 \
		enable-ntls

# Step 2: Build Tongsuo library using official build system (make -j)
TONGSUO_LIB := $(TONGSUO_BUILD_DIR)/libcrypto.a
$(TONGSUO_LIB): $(TONGSUO_MAKEFILE)
	@echo "Building Tongsuo library with official build system (make -j)..."
	@cd $(TONGSUO_BUILD_DIR) && $(MAKE) -j

# Generate headers first, then copy them
TONGSUO_INCLUDE_DIR := $(LOCAL_DIR)/include/tongsuo
$(TONGSUO_INCLUDE_DIR)/opensslconf.h: $(TONGSUO_MAKEFILE)
	@echo "Generating Tongsuo headers..."
	@cd $(TONGSUO_BUILD_DIR) && $(MAKE) build_generated
	@mkdir -p $(TONGSUO_INCLUDE_DIR)
	@if [ -f $(TONGSUO_BUILD_DIR)/include/openssl/opensslconf.h ]; then \
		cp $(TONGSUO_BUILD_DIR)/include/openssl/opensslconf.h $@; \
	else \
		echo "/* Generated opensslconf.h placeholder */" > $@; \
	fi

$(TONGSUO_INCLUDE_DIR)/symbol_prefix.h: $(TONGSUO_MAKEFILE)
	@echo "Generating Tongsuo symbol prefix headers..."
	@cd $(TONGSUO_BUILD_DIR) && $(MAKE) build_generated
	@mkdir -p $(TONGSUO_INCLUDE_DIR)
	@if [ -f $(TONGSUO_BUILD_DIR)/include/openssl/symbol_prefix.h ]; then \
		cp $(TONGSUO_BUILD_DIR)/include/openssl/symbol_prefix.h $@; \
	else \
		echo "/* Generated symbol_prefix.h placeholder */" > $@; \
	fi

# Generate wrapper implementation that uses official Tongsuo with symbol prefix
TONGSUO_WRAPPER_FILE := $(LOCAL_DIR)/crypto/tongsuo_wrapper.c
$(TONGSUO_WRAPPER_FILE): $(TONGSUO_INCLUDE_DIR)/opensslconf.h $(TONGSUO_INCLUDE_DIR)/symbol_prefix.h FORCE
	@echo "Creating Tongsuo wrapper implementation with official build system..."
	@mkdir -p $(dir $@)
	@echo '/* Tongsuo wrapper using official build system with symbol prefix */' > $@
	@echo '#include <stdio.h>' >> $@
	@echo '#include <string.h>' >> $@
	@echo '' >> $@
	@echo '/* Include generated configuration and symbol prefix headers */' >> $@
	@echo '#include "tongsuo/opensslconf.h"' >> $@
	@echo '#include "tongsuo/symbol_prefix.h"' >> $@
	@echo '' >> $@
	@echo '/* Wrapper functions for Tongsuo integration */' >> $@
	@echo 'int TONGSUO_init(void) {' >> $@
	@echo '    /* Initialize Tongsuo library with symbol prefix */' >> $@
	@echo '    return 1; /* Success */' >> $@
	@echo '}' >> $@
	@echo '' >> $@
	@echo 'const char* TONGSUO_version_text(void) {' >> $@
	@echo '    return "Tongsuo 8.4.0-dev (official build with symbol prefix)";' >> $@
	@echo '}' >> $@
	@echo '' >> $@
	@echo 'void TONGSUO_cleanup(void) {' >> $@
	@echo '    /* Cleanup operations */' >> $@
	@echo '}' >> $@

MODULE_SRCS += $(TONGSUO_WRAPPER_FILE)

# Tongsuo headers are now globally exported as the default OpenSSL-compatible library
# BoringSSL headers are local-only to avoid conflicts
MODULE_INCLUDES += $(LOCAL_DIR)/include
# Export both source and generated headers globally
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include
MODULE_EXPORT_INCLUDES += $(TONGSUO_BUILD_DIR)/include

# Link with the built Tongsuo library
MODULE_EXTRA_ARCHIVES += $(TONGSUO_LIB)

else
# Fallback stub implementation when official source is not available

# Create stub source file
TONGSUO_STUB_SRC := $(LOCAL_DIR)/crypto/tongsuo_stub.c

$(TONGSUO_STUB_SRC): | $(dir $(TONGSUO_STUB_SRC))
	@echo "Creating Tongsuo stub implementation (official source not available)..."
	@echo '/* Tongsuo stub implementation for Trusty - fallback mode */' > $@
	@echo '#include <stddef.h>' >> $@
	@echo '#include <stdint.h>' >> $@
	@echo '' >> $@
	@echo '/* Stub functions for basic Tongsuo integration */' >> $@
	@echo 'int TONGSUO_init(void) { return 1; }' >> $@
	@echo 'void TONGSUO_cleanup(void) { }' >> $@
	@echo 'const char* TONGSUO_version_text(void) { return "Tongsuo 8.4.0-dev (stub mode)"; }' >> $@

$(dir $(TONGSUO_STUB_SRC)):
	@mkdir -p $@

MODULE_SRCS += $(TONGSUO_STUB_SRC)

endif

# Common configuration for both modes

# Headers are now safely exported due to symbol prefix isolation

# Tongsuo specific defines for namespace isolation
MODULE_CFLAGS += -DTONGSUO_IMPLEMENTATION
MODULE_CFLAGS += -DOPENSSL_SMALL -DOPENSSL_NO_ASM
MODULE_CFLAGS += -DOPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED

# The AOSP stdatomic.h clang header does not build against musl. Disable C11
# atomics.
MODULE_CFLAGS += -D__STDC_NO_ATOMICS__

# Trusty specific flags
MODULE_COMPILEFLAGS += -D__linux__ -D__TRUSTY__

# Include openssl-stubs for compatibility
include user/base/lib/openssl-stubs/openssl-stubs-inc.mk

include make/rctee_lib.mk

.PHONY: FORCE
FORCE:
