/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rctee_err.h>

// Now we can safely include Tongsuo headers due to symbol prefix isolation
#include <openssl/opensslv.h>
#include <openssl/crypto.h>
#include <openssl/evp.h>

// Declare Tongsuo stub functions (still needed for wrapper functions)
extern int TONGSUO_init(void);
extern void TONGSUO_cleanup(void);
extern const char* TONGSUO_version_text(void);

static void tongsuo_init_test(void) {
    int result = TONGSUO_init();
    printf("Tongsuo init result: %d\n", result);
    if (result != 1) {
        printf("ERROR: Tongsuo init failed\n");
    } else {
        printf("SUCCESS: Tongsuo init passed\n");
    }
}

static void tongsuo_version_test(void) {
    // Test wrapper function
    const char* version = TONGSUO_version_text();
    printf("Tongsuo version (wrapper): %s\n", version ? version : "NULL");

    // Test direct header access
    printf("Tongsuo version (direct): %s\n", OPENSSL_VERSION_TEXT);
    printf("Tongsuo version number: 0x%lx\n", OPENSSL_VERSION_NUMBER);

    if (!version) {
        printf("ERROR: Tongsuo version is NULL\n");
    } else {
        printf("SUCCESS: Tongsuo version test passed\n");
    }
}

static void tongsuo_header_access_test(void) {
    printf("Testing direct header access...\n");

    // Test that we can access Tongsuo constants directly
    printf("SUCCESS: Can access OPENSSL_VERSION_TEXT: %s\n", OPENSSL_VERSION_TEXT);
    printf("SUCCESS: Can access OPENSSL_VERSION_NUMBER: 0x%x\n", OPENSSL_VERSION_NUMBER);

    // Test basic crypto initialization
    if (CRYPTO_library_init) {
        printf("SUCCESS: CRYPTO_library_init function is available\n");
    } else {
        printf("ERROR: CRYPTO_library_init function is not available\n");
    }

    printf("Header access test completed\n");
}

static void tongsuo_cleanup_test(void) {
    TONGSUO_cleanup();
    printf("SUCCESS: Tongsuo cleanup completed\n");
}

int main(void) {
    printf("=== Tongsuo Test Suite ===\n");
    printf("Testing Tongsuo integration with exported headers and symbol prefix isolation\n");

    tongsuo_init_test();
    tongsuo_version_test();
    tongsuo_header_access_test();
    tongsuo_cleanup_test();

    printf("=== All tests completed ===\n");
    return 0;
}
