/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rctee_err.h>

// Now we can safely include Tongsuo headers due to symbol prefix isolation
#include <openssl/opensslv.h>
#include <openssl/crypto.h>
#include <openssl/evp.h>

// Declare Tongsuo stub functions (still needed for wrapper functions)
extern int TONGSUO_init(void);
extern void TONGSUO_cleanup(void);
extern const char* TONGSUO_version_text(void);

static void tongsuo_init_test(void) {
    int result = TONGSUO_init();
    printf("Tongsuo init result: %d\n", result);
    if (result != 1) {
        printf("ERROR: Tongsuo init failed\n");
    } else {
        printf("SUCCESS: Tongsuo init passed\n");
    }
}

static void tongsuo_version_test(void) {
    // Test wrapper function
    const char* version = TONGSUO_version_text();
    printf("Tongsuo version (wrapper): %s\n", version ? version : "NULL");

    // Test direct header access
    printf("Tongsuo version (direct): %s\n", OPENSSL_VERSION_TEXT);
    printf("Tongsuo version number: 0x%lx\n", OPENSSL_VERSION_NUMBER);

    if (!version) {
        printf("ERROR: Tongsuo version is NULL\n");
    } else {
        printf("SUCCESS: Tongsuo version test passed\n");
    }
}

static void tongsuo_header_access_test(void) {
    printf("Testing direct header access...\n");

    // Show which header file is being used by checking the file path
    printf("Header file information:\n");
    printf("  __FILE__: %s\n", __FILE__);

    // Test that we can access version constants directly
    printf("OPENSSL_VERSION_TEXT: %s\n", OPENSSL_VERSION_TEXT);
    printf("OPENSSL_VERSION_NUMBER: 0x%x\n", OPENSSL_VERSION_NUMBER);

    // Check if this is Tongsuo or BoringSSL by looking at version string
    if (strstr(OPENSSL_VERSION_TEXT, "Tongsuo") != NULL) {
        printf("SUCCESS: Using Tongsuo headers (found 'Tongsuo' in version)\n");
    } else if (strstr(OPENSSL_VERSION_TEXT, "BoringSSL") != NULL) {
        printf("ERROR: Using BoringSSL headers (found 'BoringSSL' in version)\n");
        printf("This means header file conflict - BoringSSL headers are being used instead of Tongsuo!\n");
    } else {
        printf("UNKNOWN: Cannot determine library from version string\n");
    }

    // Check for Tongsuo-specific constants
    #ifdef TONGSUO_VERSION_TEXT
        printf("TONGSUO_VERSION_TEXT: %s\n", TONGSUO_VERSION_TEXT);
        printf("SUCCESS: Tongsuo-specific constants are available\n");
    #else
        printf("ERROR: TONGSUO_VERSION_TEXT not defined - not using Tongsuo headers\n");
    #endif

    // Check for BoringSSL-specific constants
    #ifdef BORINGSSL_FIPS
        printf("ERROR: BORINGSSL_FIPS is defined - using BoringSSL headers!\n");
    #else
        printf("SUCCESS: BORINGSSL_FIPS not defined - not using BoringSSL headers\n");
    #endif

    // Test basic crypto initialization
    if (CRYPTO_library_init) {
        printf("CRYPTO_library_init function is available\n");
    } else {
        printf("CRYPTO_library_init function is not available\n");
    }

    printf("Header access test completed\n");
}

static void tongsuo_cleanup_test(void) {
    TONGSUO_cleanup();
    printf("SUCCESS: Tongsuo cleanup completed\n");
}

int main(void) {
    printf("=== Tongsuo Test Suite ===\n");
    printf("Testing Tongsuo integration with exported headers and symbol prefix isolation\n");

    tongsuo_init_test();
    tongsuo_version_test();
    tongsuo_header_access_test();
    tongsuo_cleanup_test();

    printf("=== All tests completed ===\n");
    return 0;
}
