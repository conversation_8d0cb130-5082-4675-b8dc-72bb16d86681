/*
 * Copyright (C) 2024 The Tongsuo Project.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION
 * OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN
 * CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rctee_err.h>

// Include Tongsuo headers with symbol prefix isolation
#include <openssl/opensslv.h>
#include <openssl/crypto.h>
#include <openssl/evp.h>

// Declare Tongsuo stub functions (still needed for wrapper functions)
extern int TONGSUO_init(void);
extern void TONGSUO_cleanup(void);
extern const char* TONGSUO_version_text(void);

static void tongsuo_init_test(void) {
    int result = TONGSUO_init();
    printf("Tongsuo init result: %d\n", result);
    if (result != 1) {
        printf("ERROR: Tongsuo init failed\n");
    } else {
        printf("SUCCESS: Tongsuo init passed\n");
    }
}

static void tongsuo_version_test(void) {
    // Test wrapper function
    const char* version = TONGSUO_version_text();
    printf("Tongsuo version (wrapper): %s\n", version ? version : "NULL");

    if (!version) {
        printf("ERROR: Tongsuo version is NULL\n");
    } else {
        printf("SUCCESS: Tongsuo version test passed\n");
        // Check if this contains "Tongsuo" to verify we're using the right library
        if (strstr(version, "Tongsuo") != NULL) {
            printf("SUCCESS: Confirmed using Tongsuo library (found 'Tongsuo' in version)\n");
        } else {
            printf("WARNING: Version string doesn't contain 'Tongsuo': %s\n", version);
        }
    }
}

static void tongsuo_symbol_prefix_test(void) {
    printf("Testing symbol prefix isolation...\n");

    // Show which source file this is
    printf("Source file: %s\n", __FILE__);

    // Test that we can call Tongsuo functions (symbol prefix isolation works automatically)
    const char* version = OpenSSL_version(OPENSSL_VERSION);
    if (version) {
        printf("SUCCESS: OpenSSL_version() works - symbol prefix isolation is working\n");
        printf("Version: %s\n", version);
    } else {
        printf("ERROR: OpenSSL_version() returned NULL\n");
    }

    // Test that we can call other Tongsuo functions
    int result = OPENSSL_init_crypto(OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
    printf("OPENSSL_init_crypto() result: %d\n", result);

    // Test crypto context creation
    void* ctx = EVP_MD_CTX_new();
    if (ctx) {
        printf("SUCCESS: EVP_MD_CTX_new() works\n");
        EVP_MD_CTX_free(ctx);
        printf("SUCCESS: EVP_MD_CTX_free() works\n");
    } else {
        printf("ERROR: EVP_MD_CTX_new() returned NULL\n");
    }

    printf("Symbol prefix test completed\n");
}

static void tongsuo_cleanup_test(void) {
    // Modern OpenSSL/Tongsuo doesn't require explicit cleanup
    printf("SUCCESS: Tongsuo cleanup completed (no explicit cleanup needed)\n");
}

int main(void) {
    printf("=== Tongsuo Test Suite ===\n");
    printf("Testing Tongsuo integration with exported headers and symbol prefix isolation\n");

    tongsuo_init_test();
    tongsuo_version_test();
    tongsuo_symbol_prefix_test();
    tongsuo_cleanup_test();

    printf("=== All tests completed ===\n");
    return 0;
}
